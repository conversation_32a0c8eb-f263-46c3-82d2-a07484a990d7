/// <reference types="vite/client" />
/// <reference types="react/jsx-runtime" />

// CSS 文件类型声明
declare module '*.css' {
  const content: string;
  export default content;
}

// CSS 模块类型声明
declare module '*.module.css' {
  const classes: { readonly [key: string]: string };
  export default classes;
}

declare module '*.module.scss' {
  const classes: { readonly [key: string]: string };
  export default classes;
}

declare module '*.module.sass' {
  const classes: { readonly [key: string]: string };
  export default classes;
}
