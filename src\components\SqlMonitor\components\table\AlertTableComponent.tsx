import type { TableColumnsType, TableProps } from 'antd';
import type { TableRowSelection } from 'antd/es/table/interface';
import { Pagination, Table } from 'antd';
import React from 'react';

import type { TaskAlert } from '@/components/SqlMonitor/types';
import { PAGE_SIZE_OPTIONS, PAGINATION_LOCALE, TABLE_SCROLL_CONFIG, TABLE_SIZE } from '@/components/SqlMonitor/constants';
import { tableStyles } from '@/components/SqlMonitor/styles';

interface AlertTableComponentProps {
  columns: TableColumnsType<TaskAlert>;
  data: TaskAlert[];
  loading: boolean;
  total: number;
  pagination: {
    current: number;
    page_size: number;
  };
  rowSelection: TableRowSelection<TaskAlert>;
  tableScrollY: number;
  onTableChange?: TableProps<TaskAlert>['onChange'];
  onPaginationChange: (page: number, pageSize: number) => void;
}

/**
 * 监控项表格主体组件
 * 包含表格和分页功能
 */
export const AlertTableComponent: React.FC<AlertTableComponentProps> = ({ columns, data, loading, total, pagination, rowSelection, tableScrollY, onTableChange, onPaginationChange }) => {
  return (
    <>
      {/* 表格区域 */}
      <div className="flex-1 overflow-hidden">
        <div className="flex-1 overflow-hidden min-h-0">
          <Table
            columns={columns}
            dataSource={data}
            loading={loading}
            rowKey="id"
            rowSelection={rowSelection}
            pagination={false}
            size={TABLE_SIZE}
            scroll={{
              ...TABLE_SCROLL_CONFIG,
              y: tableScrollY,
            }}
            onChange={onTableChange}
            className={`custom-table h-full`}
          />
        </div>
      </div>

      {/* 分页区域 - 固定在表格底部 */}
      <div className="flex-shrink-0 h-12 border-t border-gray-200 bg-gray-50 flex items-center">
        <div className="px-4 w-full h-full flex items-center">
          <div className="flex justify-between items-center w-full h-full">
            <div className={tableStyles.dataStats}>
              <span>共 {total} 条数据</span>
            </div>
            <Pagination
              current={pagination.current}
              pageSize={pagination.page_size}
              total={total}
              showSizeChanger
              showQuickJumper
              showTotal={(total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`}
              className="custom-pagination"
              pageSizeOptions={PAGE_SIZE_OPTIONS}
              locale={PAGINATION_LOCALE}
              onChange={(page, pageSize) => {
                onPaginationChange(page, pageSize);
              }}
              onShowSizeChange={(current, size) => {
                onPaginationChange(current, size);
              }}
              size="small"
              hideOnSinglePage={false}
              responsive={true}
              showLessItems={false}
            />
          </div>
        </div>
      </div>
    </>
  );
};
