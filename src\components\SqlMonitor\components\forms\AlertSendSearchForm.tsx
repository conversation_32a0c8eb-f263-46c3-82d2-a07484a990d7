import React from 'react';
import { Form, Input, Select, Button, Row, Col } from 'antd';
import { SearchOutlined, ReloadOutlined } from '@ant-design/icons';
import type { FormInstance } from 'antd';

import type { AlertSendSearchParams } from '../../types';
import { tableStyles } from '../../styles';

const { Option } = Select;

interface AlertSendQuickSearchFormProps {
  form: FormInstance;
  onSubmit: (values: AlertSendSearchParams) => void;
  onReset: () => void;
}

/**
 * 告警发送快速搜索表单组件
 * 提供告警发送的快速搜索功能
 */
export const AlertSendQuickSearchForm: React.FC<AlertSendQuickSearchFormProps> = ({ form, onSubmit, onReset }) => {
  const handleSubmit = (values: Partial<AlertSendSearchParams>) => {
    console.log('告警发送搜索参数:', values);
    onSubmit(values);
  };

  const handleReset = () => {
    form.resetFields();
    onReset();
  };

  return (
    <div className={tableStyles.searchSection}>
      <Form form={form} onFinish={handleSubmit} className="w-full">
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={8} md={6}>
            <div className="flex flex-col gap-1 h-full justify-center">
              <Form.Item name="name" className="mb-0">
                <Input
                  key="alert-send-search-name-input"
                  placeholder="请输入发送名称"
                  prefix={<SearchOutlined className="text-gray-400" />}
                  allowClear
                  className="rounded-md"
                  autoComplete="off"
                  autoCorrect="off"
                  autoCapitalize="off"
                  spellCheck={false}
                />
              </Form.Item>
            </div>
          </Col>
          <Col xs={24} sm={8} md={6}>
            <div className="flex flex-col gap-1 h-full justify-center">
              <Form.Item name="receive_type" className="mb-0">
                <Select placeholder="请选择接收类型" allowClear className="w-full rounded-md">
                  <Option value="kafka">Kafka</Option>
                  <Option value="prometheus">Prometheus</Option>
                </Select>
              </Form.Item>
            </div>
          </Col>
          <Col xs={24} sm={12} md={12}>
            <div className="flex flex-col gap-1 h-full justify-center">
              <div className="flex gap-2 items-center h-full">
                <Button type="primary" htmlType="submit" icon={<SearchOutlined />} className={tableStyles.buttonPrimary}>
                  搜索
                </Button>
                <Button onClick={handleReset} className="rounded-md flex-1" icon={<ReloadOutlined />}>
                  重置
                </Button>
              </div>
            </div>
          </Col>
        </Row>
      </Form>
    </div>
  );
};
